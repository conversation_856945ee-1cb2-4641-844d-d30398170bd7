#include "BufferPharser.h"
#include "UDPModule.h"
#include "UDPDriver.h"
extern CUDPDriver* thePlugin;

CBufferPharser::CBufferPharser(CUDPModule* _aModule)
:m_bigPharser(_aModule)
,m_litPharser(_aModule)
,m_module(_aModule)
,m_iPackageNumber(-1)
,m_bGetFirstPackage(false)
,m_iMissPackageCount(0)
{
}

CBufferPharser::~CBufferPharser(void)
{
}
/**********************************************************************
*  概述:  记录缓冲区中数据包的个数
*  返回值:  [描述]	无
*  参数列表:
*    _packageNumber: [IN]   [描述] 缓冲区中数据包的个数
*
*  版本历史		
*       1.0   2010:6:28 9:38   Shenhuaibin    实现
*  
**********************************************************************/	
void CBufferPharser::setPackageNumber(int _packageNumber)
{

	m_iPackageNumber = _packageNumber;
}
/**********************************************************************
*  概述:  补充丢失的包
*  返回值: void [描述]	无
*  参数列表:
*    _number: [IN]   [描述]  需要补充的包个个数
*
*  版本历史		
*       1.0   2010:6:23 14:28   Shenhuaibin    实现
*  
**********************************************************************/	
void CBufferPharser::completePackage(int _number)
{
	std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CBufferPharser::completePackage"));
	//log->Fatal ()<<"端口 ["<<m_module->m_moduleConfig.iPort<<"] 模块 ["<<m_module->m_moduleConfig.iModuleIndex<<"] 补包 ["<<_number<<"] 个"<<endl;
	double		fRes = -1.0;
	int         iOffset = -1;

	for(;_number-- !=0;)
	{
		//std::map<std::string,std::vector<float>>::iterator valuesItor = NULL;
		std::map<std::string,std::vector<float>>::iterator valuesItor;

		for (std::vector<CUDPModule::ModuleConfig::UDPSignal>::iterator signalItor = m_module->m_moduleConfig.vSignals.begin ();\
			signalItor != m_module->m_moduleConfig.vSignals.end ();\
			++ signalItor)
		{
			// 获得到存储该信号的数组
			for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
			{
				signalItor->valuesItor->second.push_back(fRes);
			}
		}
	}
}
/**********************************************************************
*  概述:  解析电文内容
*  返回值: bool [描述] 是否是脏数据
*  参数列表:
*    _allTelegram: [IN]   [描述] 所有电文内存区
*
*  版本历史		
*       1.0   2010:6:4 14:08   Shenhuaibin    实现
*  
**********************************************************************/	
bool CBufferPharser::pharser(IN std::vector<unsigned char>& _allTelegram, bool isMulticast)
{
	//std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CBufferPharser::pharser"));
	//log->Info ("开始解析电文");

	// 补充丢失的包.
	if (_allTelegram.size () == 0)
	{
		// 让出cpu时间让其他线程继续执行.万一接收线程刚恢复网络为正常
		Sleep (0);
		// 设置网络异常
		if(_allTelegram.size () == 0)
		{	
			if(!m_bGetFirstPackage)
			{
				//log->Fatal ("尚未接受到数据包");
				return false;
			}
			
			m_module->setCurrentStatus(false);
			// 800毫秒才开始补包
			if(m_iMissPackageCount ++ >0)
				completePackage(m_iPackageNumber);
			//log->Fatal ("本数据为一批脏数据");
			return false;
		}
	}
	// 恢复正常.丢包记录数恢复为0
	m_iMissPackageCount = 0;
	//log->Info ("检测网络状态 [ 网络正常 ],开始解析报文");
	// 标记为已经收到第一个报文
	m_bGetFirstPackage = true;
	// 正常解析
	if (m_module->m_moduleConfig.eNBO == CUDPModule::ModuleConfig::nboType::BIG_ENDIAN)
	{
		if (isMulticast)
		{
			m_bigPharser.pharserMulticast(_allTelegram);
		}
		else
		{
			//log->Info ("电文需要高低字节转化");
			m_bigPharser.pharser(_allTelegram);
		}

	}else if (m_module->m_moduleConfig.eNBO == CUDPModule::ModuleConfig::nboType::LIT_ENDIAN)
	{
		if (isMulticast)
		{
			m_litPharser.pharserMulticast(_allTelegram);
		}
		else
		{
			//log->Info ("电文不需要高低字节转化");
			m_litPharser.pharser(_allTelegram);
		}

	}

	return true;
}

/**********************************************************************
*  概述:  解析需要高低自己转化的报文
*  返回值: bool [描述] 转化是否成功
*  参数列表:
*    _allTelegram: [IN]   [描述] 报文
*    _mValues    : [OUT]   [描述] 输出解析后的数值
*
*  版本历史		
*       1.0   2010:6:10 17:50   Shenhuaibin    实现
*  
**********************************************************************/	
bool CBufferPharser::CBigPharser::pharser(IN std::vector<unsigned char>& _allTelegram)
{
	//std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CBufferPharser::CBigPharser::pharser"));

	DWORD dwStart = ::GetTickCount();

		/*
	BOOL	bool				1	0	布尔量，占1个bit	
	BYTE	unsigned char		8	1	无符号字符型，占1个字节	

	WORD	unsigned short int	16	2	无符号短整型，占2个字节	
	DWORD	unsigned int		32	3	无符号整型，占4个字节	
	INT		short int			16	4	短整型，占2个字节	
	DINT	int					32	5	整型，占4个字节	
	REAL	float				32	6	浮点型，占4个字节	
	*/
	// SamplePoints
	// Offset
	double		fRes = -1.0;
	int			bit32 = 0;
	short int	bit16 = 0;
	unsigned char bit8 = 0;

	unsigned short int UDPWORD = 0;
	unsigned int       UDPDWORD = 0;
	short int	       UDPINT   = 0;
	int                UDPDINT  = 0;

	int			iOffset = 0;
	//int         intPos  = 0;
	/************************************************************************/
	int toatlLen = _allTelegram.size();
	int _index   = 0;
	/************************************************************************/
	for (std::vector<CUDPModule::ModuleConfig::UDPSignal>::iterator signalItor = m_module->m_moduleConfig.vSignals.begin ();\
		signalItor != m_module->m_moduleConfig.vSignals.end ();\
		++ signalItor)
	{
		// 获得到存储该信号的数组
		//m_module->m_moduleConfig.mSignalsValue.find (std::map<std::string,std::vector<float>>::key_type(signalItor->strNo));
		//取整数部分.
		//intPos  = (((int)(signalItor->fOffset*10))+1)/10;
		
		switch(signalItor->eDataType)
		{
			case DataType::UDP_BOOL:	/*1bit*/;
			{
				do
				{
					// 取小数位
					//int bitPos = ((((int)(signalItor->fOffset*100))%100)+1)/10;

					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						//memcpy(&bit8,&_allTelegram[_index+ signalItor->fOffset_int + iOffset],1);
						bit8 = *(unsigned char*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset]);
						fRes = bit(bit8,signalItor->fOffset_dec);
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_BOOL  value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset<<" bitPos = "<<signalItor->fOffset_dec<<endl;
					}
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_BYTE:	/*8bit*/;
			{
				do
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						//memcpy(&bit8,&_allTelegram[_index+ signalItor->fOffset_int + iOffset],1);
						bit8 = *(unsigned char*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset]);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = bit8* signalItor->fGain;
						else
							fRes = bit8;
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_BYTE  value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset<<endl;
					}
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_WORD:	/*16bit*/;
			{
				do
				{
					// 是否是多个采样值
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						//memcpy(&bit16,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2],2);
						bit16 = *(short int*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2]);
						UDPWORD = cdr_int16_to(bit16);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = UDPWORD * signalItor->fGain;
						else
							fRes = UDPWORD;
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_WORD  value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*2<<endl;
					}
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_DWORD:/*32bit*/;
			{
				do
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						//memcpy(&bit32,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4],4);
						bit32 = *(int*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4]);
						UDPDWORD = cdr_int32_to(bit32);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = UDPDWORD * signalItor->fGain;
						else
							fRes = UDPDWORD;
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_DWORD value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*4<<endl;
					}
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_INT:/*short int 16bit*/;
			{
				do
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						//memcpy(&bit16,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2],2);
						bit16 = *(short int*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2]);
						UDPINT = cdr_int16_to(bit16);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = UDPINT * signalItor->fGain;
						else
							fRes = UDPINT;
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_INT value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*2<<endl;
					}
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_DINT:/*int 32bit*/;
		    {
				do
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						//memcpy(&bit32,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4],4);
						bit32 = *(int*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4]);
						UDPDINT = cdr_int32_to(bit32);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = UDPDINT * signalItor->fGain;
						else
							fRes = UDPDINT;
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_DINT value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*4<<endl;
					}
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;		
		    }

			case DataType::UDP_REAL:	/*32bit*/;
			{
				do
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						//memcpy(&bit32,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4],4);
						bit32 = *(int*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4]);
						bit32 = cdr_int32_to(bit32);
						fRes = *((float*)&bit32);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = fRes * signalItor->fGain;
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_REAL value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*4<<endl;
					}
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
		}
	}

	DWORD dwEnd =::GetTickCount();
	//log->Info ()<<"Big:本次解析 ["<<_allTelegram.size()/*/m_module->m_moduleConfig.iTelegramLength*/<<"] 个字节,用时:"<<dwEnd-dwStart<<endl;
	return true;
}


bool CBufferPharser::CBigPharser::pharserMulticast(IN std::vector<unsigned char>& _allTelegram)
{
	//std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CBufferPharser::CBigPharser::pharser"));

	DWORD dwStart = ::GetTickCount();
	double		fRes = -1.0;
	int			bit32 = 0;
	short int	bit16 = 0;
	unsigned char bit8 = 0;

	unsigned short int UDPWORD = 0;
	unsigned int       UDPDWORD = 0;
	short int	       UDPINT = 0;
	int                UDPDINT = 0;

	int toatlLen = _allTelegram.size();
	for (std::vector<CUDPModule::ModuleConfig::UDPSignal>::iterator signalItor = m_module->m_moduleConfig.vSignals.begin(); \
		signalItor != m_module->m_moduleConfig.vSignals.end(); \
		++signalItor)
	{
		switch (signalItor->eDataType)
		{
			case DataType::UDP_BOOL:	/*1bit*/
				if (signalItor->fOffset_int + 1 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					bit8 = *(unsigned char*)(&_allTelegram[signalItor->fOffset_int]);
					fRes = bit(bit8, signalItor->fOffset_dec);
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
			case DataType::UDP_BYTE:	/*8bit*/
				if (signalItor->fOffset_int + 1 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					bit8 = *(unsigned char*)(&_allTelegram[signalItor->fOffset_int]);
					if (signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = bit8* signalItor->fGain;
					else
						fRes = bit8;
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
			case DataType::UDP_WORD:	/*16bit*/
				if (signalItor->fOffset_int + 2 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					bit16 = *(short int*)(&_allTelegram[signalItor->fOffset_int]);
					UDPWORD = cdr_int16_to(bit16);
					if (signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = UDPWORD * signalItor->fGain;
					else
						fRes = UDPWORD;
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
			case DataType::UDP_DWORD:/*32bit*/
				if (signalItor->fOffset_int + 4 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					bit32 = *(int*)(&_allTelegram[signalItor->fOffset_int]);
					UDPDWORD = cdr_int32_to(bit32);
					if (signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = UDPDWORD * signalItor->fGain;
					else
						fRes = UDPDWORD;
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
			case DataType::UDP_INT:/*short int 16bit*/
				if (signalItor->fOffset_int + 2 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					bit16 = *(short int*)(&_allTelegram[signalItor->fOffset_int]);
					UDPINT = cdr_int16_to(bit16);
					if (signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = UDPINT * signalItor->fGain;
					else
						fRes = UDPINT;
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
			case DataType::UDP_DINT:/*int 32bit*/
				if (signalItor->fOffset_int + 4 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					bit32 = *(int*)(&_allTelegram[signalItor->fOffset_int]);
					UDPDINT = cdr_int32_to(bit32);
					if (signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = UDPDINT * signalItor->fGain;
					else
						fRes = UDPDINT;
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
			case DataType::UDP_REAL:	/*32bit*/
				if (signalItor->fOffset_int + 4 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					bit32 = *(int*)(&_allTelegram[signalItor->fOffset_int]);
					bit32 = cdr_int32_to(bit32);
					fRes = *((float*)&bit32);
					if (signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = fRes * signalItor->fGain;
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
		}
	}

	DWORD dwEnd = ::GetTickCount();
	//log->Info ()<<"Big:本次解析 ["<<_allTelegram.size()/*/m_module->m_moduleConfig.iTelegramLength*/<<"] 个字节,用时:"<<dwEnd-dwStart<<endl;
	return true;
}
/**********************************************************************
*  概述:  解析需要高低自己转化的一段报文
*  返回值: bool [描述] 解析过程是否成功
*  参数列表:
*    _allTelegram  : [IN]   [描述] 所有电文
*    _index        : [IN]   [描述] 电文的起点
*
*  版本历史		
*       1.0   2010:6:10 17:56   Shenhuaibin    实现
*  
**********************************************************************/	
bool CBufferPharser::CBigPharser::pharserOneTelegram(vector<unsigned char>& _allTelegram,int _index)
{
	/*
	BOOL	bool				1	0	布尔量，占1个bit	
	BYTE	unsigned char		8	1	无符号字符型，占1个字节	

	WORD	unsigned short int	16	2	无符号短整型，占2个字节	
	DWORD	unsigned int		32	3	无符号整型，占4个字节	
	INT		short int			16	4	短整型，占2个字节	
	DINT	int					32	5	整型，占4个字节	
	REAL	float				32	6	浮点型，占4个字节	
	*/
	// SamplePoints
	// Offset
	std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CBufferPharser::CBigPharser::pharserOneTelegram"));
	log->Debug ()<<"正在解析一段完整报文: index:  "<<_index<<endl;
	double	fRes = -1.0;
	int			bit32 = 0;
	short int	bit16 = 0;
	unsigned char bit8 = 0;

	unsigned short int UDPWORD = 0;
	unsigned int       UDPDWORD = 0;
	short int	       UDPINT   = 0;
	int                UDPDINT  = 0;

	int			iOffset = 0;
	//int         intPos  = 0;

	for (std::vector<CUDPModule::ModuleConfig::UDPSignal>::iterator signalItor = m_module->m_moduleConfig.vSignals.begin ();\
		signalItor != m_module->m_moduleConfig.vSignals.end ();\
		++ signalItor)
	{
		// 获得到存储该信号的数组
		//m_module->m_moduleConfig.mSignalsValue.find (std::map<std::string,std::vector<float>>::key_type(signalItor->strNo));
		//取整数部分.
		//intPos  = (((int)(signalItor->fOffset*10))+1)/10;
		
		switch(signalItor->eDataType)
		{
			case DataType::UDP_BOOL:	/*1bit*/;
			{
				// 取小数位
				//int bitPos = ((((int)(signalItor->fOffset*100))%100)+1)/10;

				for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
				{
					//memcpy(&bit8,&_allTelegram[_index+ signalItor->fOffset_int + iOffset],1);
					bit8 = *(unsigned char*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset]);
					fRes = bit(bit8,signalItor->fOffset_dec);
					signalItor->valuesItor->second.push_back(fRes);
					log->Debug ()<<"正在解析一段完整报文: UDP_BOOL  value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset<<" bitPos = "<<signalItor->fOffset_dec<<endl;
				}
				break;
			}
			case DataType::UDP_BYTE:	/*8bit*/;
			{
				for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
				{
					//memcpy(&bit8,&_allTelegram[_index+ signalItor->fOffset_int + iOffset],1);
					bit8 = *(unsigned char*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset]);
					if(signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = bit8* signalItor->fGain;
					else
						fRes = bit8;
					signalItor->valuesItor->second.push_back(fRes);
					log->Debug ()<<"正在解析一段完整报文: UDP_BYTE  value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset<<endl;
				}
				break;
			}
			case DataType::UDP_WORD:	/*16bit*/;
			{
				// 是否是多个采样值
				for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
				{
					//memcpy(&bit16,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2],2);
					bit16 = *(short int*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2]);
					UDPWORD = cdr_int16_to(bit16);
					if(signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = UDPWORD * signalItor->fGain;
					else
						fRes = UDPWORD;
					signalItor->valuesItor->second.push_back(fRes);
					log->Debug ()<<"正在解析一段完整报文: UDP_WORD  value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*2<<endl;
				}
				break;
			}
			case DataType::UDP_DWORD:/*32bit*/;
			{
				for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
				{
					//memcpy(&bit32,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4],4);
					bit32 = *(int*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4]);
					UDPDWORD = cdr_int32_to(bit32);
					if(signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = UDPDWORD * signalItor->fGain;
					else
						fRes = UDPDWORD;
					signalItor->valuesItor->second.push_back(fRes);
					log->Debug ()<<"正在解析一段完整报文: UDP_DWORD value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*4<<endl;
				}
				break;
			}
			case DataType::UDP_INT:/*short int 16bit*/;
			{
				for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
				{
					//memcpy(&bit16,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2],2);
					bit16 = *(short int*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2]);
					UDPINT = cdr_int16_to(bit16);
					if(signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = UDPINT * signalItor->fGain;
					else
						fRes = UDPINT;
					signalItor->valuesItor->second.push_back(fRes);
					log->Debug ()<<"正在解析一段完整报文: UDP_INT value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*2<<endl;
				}
				break;
			}
			case DataType::UDP_DINT:/*int 32bit*/;
		    {
				for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
				{
					//memcpy(&bit32,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4],4);
					bit32 = *(int*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4]);
					UDPDINT = cdr_int32_to(bit32);
					if(signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = UDPDINT * signalItor->fGain;
					else
						fRes = UDPDINT;
					signalItor->valuesItor->second.push_back(fRes);
					log->Debug ()<<"正在解析一段完整报文: UDP_DINT value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*4<<endl;
				}
				break;		
		    }

			case DataType::UDP_REAL:	/*32bit*/;
			{
				for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
				{
					//memcpy(&bit32,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4],4);
					bit32 = *(int*)(&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4]);
					bit32 = cdr_int32_to(bit32);
					fRes = *((float*)&bit32);
					if(signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = fRes * signalItor->fGain;
					signalItor->valuesItor->second.push_back(fRes);
					log->Debug ()<<"正在解析一段完整报文: UDP_REAL value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*4<<endl;
				}
				break;
			}
		}

	}
	return true;
}
/**********************************************************************
*  概述:  解析不需要高低自己转化的报文
*  返回值: bool [描述] 
*  参数列表:
*    _allTelegram: [IN]   [描述] 报文
*    _mValues    : [OUT]   [描述] 输出解析后的数值
*
*  版本历史		
*       1.0   2010:6:10 17:51   Shenhuaibin    实现
*  
**********************************************************************/	
bool CBufferPharser::CLitPharser::pharser(IN std::vector<unsigned char>& _allTelegram/*,std::map<std::string,std::vector<float>>& _mValues*/)
{
	//std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CBufferPharser::CLitPharser::pharser"));
	DWORD dwStart = ::GetTickCount();
	
	/*
	BOOL	bool				1	0	布尔量，占1个bit	
	BYTE	unsigned char		8	1	无符号字符型，占1个字节	

	WORD	unsigned short int	16	2	无符号短整型，占2个字节	
	DWORD	unsigned int		32	3	无符号整型，占4个字节	
	INT		short int			16	4	短整型，占2个字节	
	DINT	int					32	5	整型，占4个字节	
	REAL	float				32	6	浮点型，占4个字节	
	*/
	// SamplePoints
	// Offset
	double		fRes = -1.0;
	int         iOffset = -1;
	unsigned char bit8 = 0;

	unsigned short int UDPWORD = 0;
	unsigned int       UDPDWORD = 0;
	short int	       UDPINT   = 0;
	int                UDPDINT  = 0;

	//int         intPos  = 0;
/************************************************************************/
	int toatlLen = _allTelegram.size();
	int _index   = 0;
/************************************************************************/

	for (std::vector<CUDPModule::ModuleConfig::UDPSignal>::iterator signalItor = m_module->m_moduleConfig.vSignals.begin ();\
		signalItor != m_module->m_moduleConfig.vSignals.end ();\
		++ signalItor)
	{
		// 获得到存储该信号的数组
		//m_module->m_moduleConfig.mSignalsValue.find (std::map<std::string,std::vector<float>>::key_type(signalItor->strNo));
		
		//取整数部分.
		//intPos  = (((int)(signalItor->fOffset*10))+1)/10;

		switch(signalItor->eDataType)
		{
			case DataType::UDP_BOOL:	/*1bit*/;
			{
				do 
				{
					// 取小数位
					//int bitPos = ((((int)(signalItor->fOffset*100))%100)+1)/10;
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						bit8 = *(unsigned char*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset];
						//memcpy(&bit8,&_allTelegram[_index+ signalItor->fOffset_int + iOffset],1);
						fRes = bit(bit8,signalItor->fOffset_dec);
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_BOOL  value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset<<" bitPos = "<<signalItor->fOffset_dec<<endl;
					}	
					
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_BYTE:	/*8bit*/;
			{
				do 
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						bit8 = *(unsigned char*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset];
						//memcpy(&bit8,&_allTelegram[_index+ signalItor->fOffset_int + iOffset],1);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = bit8* signalItor->fGain;
						else
							fRes = bit8;
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_BYTE  value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset<<endl;
					}	

					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_WORD:	/*16bit*/;
			{
				do 
				{
					// 是否是多个采样值
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						UDPWORD = *(unsigned short int*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2];
						//memcpy(&UDPWORD,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2],2);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = UDPWORD * signalItor->fGain;
						else
							fRes = UDPWORD;
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_WORD  value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*2<<endl;
					}
	
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_DWORD:/*32bit*/;
			{
				do 
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						UDPDWORD = *(unsigned int*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4];
						//memcpy(&UDPDWORD,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4],4);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = UDPDWORD * signalItor->fGain;
						else
							fRes = UDPDWORD;
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_DWORD value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*4<<endl;
					}
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_INT:/*short int 16bit*/;
			{
				do 
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						UDPINT = *(short int*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2];
						//memcpy(&UDPINT,&_allTelegram[_index+ signalItor->fOffset_int/* + iOffset*2*/],2);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = UDPINT * signalItor->fGain;
						else
							fRes = UDPINT;
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_INT value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*2<<endl;
					}

					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_DINT:/*int 32bit*/;
			{
				do 
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						UDPDINT = *(int*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4];
						//memcpy(&UDPDINT,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4],4);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = UDPDINT * signalItor->fGain;
						else
							fRes = UDPDINT;
						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_DINT value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*4<<endl;
					}

					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}

			case DataType::UDP_REAL:	/*32bit*/;
			{
				do 
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						fRes = *(float*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4];
						//memcpy(&fRes,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4],4);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = fRes * signalItor->fGain;

						signalItor->valuesItor->second.push_back(fRes);
						//log->Debug ()<<"正在解析一段完整报文: UDP_REAL value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*4<<endl;
					}

					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
		}
	}
	DWORD dwEnd =::GetTickCount();
	//log->Info()<<"Little:本次解析 ["<<toatlLen/*/m_module->m_moduleConfig.iTelegramLength*/<<"] 个字节,用时:"<<dwEnd-dwStart<<endl;

	return true;
}

bool CBufferPharser::CLitPharser::pharserMulticast(IN std::vector<unsigned char>& _allTelegram/*,std::map<std::string,std::vector<float>>& _mValues*/)
{
	//std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CBufferPharser::CLitPharser::pharser"));
	DWORD dwStart = ::GetTickCount();

	double		fRes = -1.0;
	unsigned char bit8 = 0;

	unsigned short int UDPWORD = 0;
	unsigned int       UDPDWORD = 0;
	short int	       UDPINT = 0;
	int                UDPDINT = 0;

	int toatlLen = _allTelegram.size();

	for (std::vector<CUDPModule::ModuleConfig::UDPSignal>::iterator signalItor = m_module->m_moduleConfig.vSignals.begin(); \
		signalItor != m_module->m_moduleConfig.vSignals.end(); \
		++signalItor)
	{
		switch (signalItor->eDataType)
		{
			case DataType::UDP_BOOL:	/*1bit*/
				if (signalItor->fOffset_int + 1 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					bit8 = *(unsigned char*)&_allTelegram[signalItor->fOffset_int];
					fRes = bit(bit8, signalItor->fOffset_dec);
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
			case DataType::UDP_BYTE:	/*8bit*/
				if (signalItor->fOffset_int + 1 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					bit8 = *(unsigned char*)&_allTelegram[signalItor->fOffset_int];
					if (signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = bit8* signalItor->fGain;
					else
						fRes = bit8;
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
			case DataType::UDP_WORD:	/*16bit*/
				if (signalItor->fOffset_int + 2 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					UDPWORD = *(unsigned short int*)&_allTelegram[signalItor->fOffset_int];
					if (signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = UDPWORD * signalItor->fGain;
					else
						fRes = UDPWORD;
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
			case DataType::UDP_DWORD:/*32bit*/
				if (signalItor->fOffset_int + 4 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					UDPDWORD = *(unsigned int*)&_allTelegram[signalItor->fOffset_int];
					if (signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = UDPDWORD * signalItor->fGain;
					else
						fRes = UDPDWORD;
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
			case DataType::UDP_INT:/*short int 16bit*/
				if (signalItor->fOffset_int + 2 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					UDPINT = *(short int*)&_allTelegram[signalItor->fOffset_int];
					if (signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = UDPINT * signalItor->fGain;
					else
						fRes = UDPINT;
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
			case DataType::UDP_DINT:/*int 32bit*/
				if (signalItor->fOffset_int + 4 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					UDPDINT = *(int*)&_allTelegram[signalItor->fOffset_int];
					if (signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = UDPDINT * signalItor->fGain;
					else
						fRes = UDPDINT;
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
			case DataType::UDP_REAL:	/*32bit*/
				if (signalItor->fOffset_int + 4 > toatlLen)
				{
					signalItor->valuesItor->second.push_back(-99999);//
				}
				else
				{
					fRes = *(float*)&_allTelegram[signalItor->fOffset_int];
					if (signalItor->isAnalog&&!signalItor->fGain_is_1)
						fRes = fRes * signalItor->fGain;
					signalItor->valuesItor->second.push_back(fRes);
				}
				break;
		}
	}
	DWORD dwEnd = ::GetTickCount();
	//log->Info()<<"Little:本次解析 ["<<toatlLen/*/m_module->m_moduleConfig.iTelegramLength*/<<"] 个字节,用时:"<<dwEnd-dwStart<<endl;

	return true;
}

/**********************************************************************
*  概述:  解析不需要高低自己转化的一段报文
*  返回值: bool [描述] 解析过程是否成功
*  参数列表:
*    _allTelegram  : [IN]   [描述] 所有电文
*    _index        : [IN]   [描述] 电文的起点
*
*  版本历史		
*       1.0   2010:6:4 17:43   Shenhuaibin    实现
*  
**********************************************************************/	
bool CBufferPharser::CLitPharser::pharserOneTelegram(vector<unsigned char>& _allTelegram,int _index/*,std::map<std::string,std::vector<float>>& _mValues*/)
{
	/*
	BOOL	bool				1	0	布尔量，占1个bit	
	BYTE	unsigned char		8	1	无符号字符型，占1个字节	

	WORD	unsigned short int	16	2	无符号短整型，占2个字节	
	DWORD	unsigned int		32	3	无符号整型，占4个字节	
	INT		short int			16	4	短整型，占2个字节	
	DINT	int					32	5	整型，占4个字节	
	REAL	float				32	6	浮点型，占4个字节	
	*/
	// SamplePoints
	// Offset
	std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CBufferPharser::CLitPharser::pharserOneTelegram"));
	log->Debug ()<<"正在解析一段完整报文: index:  "<<_index<<endl;
	float		fRes = -1.0;
	int         iOffset = -1;
	unsigned char bit8 = 0;

	unsigned short int UDPWORD = 0;
	unsigned int       UDPDWORD = 0;
	short int	       UDPINT   = 0;
	int                UDPDINT  = 0;

	//int         intPos  = 0;
/************************************************************************/
	int toatlLen = _allTelegram.size();
/************************************************************************/

	for (std::vector<CUDPModule::ModuleConfig::UDPSignal>::iterator signalItor = m_module->m_moduleConfig.vSignals.begin ();\
		signalItor != m_module->m_moduleConfig.vSignals.end ();\
		++ signalItor)
	{
		// 获得到存储该信号的数组
		//m_module->m_moduleConfig.mSignalsValue.find (std::map<std::string,std::vector<float>>::key_type(signalItor->strNo));
		
		//取整数部分.
		//intPos  = (((int)(signalItor->fOffset*10))+1)/10;

		switch(signalItor->eDataType)
		{
			case DataType::UDP_BOOL:	/*1bit*/;
			{
				do 
				{
					// 取小数位
					//int bitPos = ((((int)(signalItor->fOffset*100))%100)+1)/10;
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						bit8 = *(unsigned char*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset];
						//memcpy(&bit8,&_allTelegram[_index+ signalItor->fOffset_int + iOffset],1);
						fRes = bit(bit8,signalItor->fOffset_dec);
						signalItor->valuesItor->second.push_back(fRes);
						log->Debug ()<<"正在解析一段完整报文: UDP_BOOL  value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset<<" bitPos = "<<signalItor->fOffset_dec<<endl;
					}	
					
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_BYTE:	/*8bit*/;
			{
				do 
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						bit8 = *(unsigned char*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset];
						//memcpy(&bit8,&_allTelegram[_index+ signalItor->fOffset_int + iOffset],1);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = bit8* signalItor->fGain;
						else
							fRes = bit8;
						signalItor->valuesItor->second.push_back(fRes);
						log->Debug ()<<"正在解析一段完整报文: UDP_BYTE  value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset<<endl;
					}	

					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_WORD:	/*16bit*/;
			{
				do 
				{
					// 是否是多个采样值
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						UDPWORD = *(unsigned short int*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2];
						//memcpy(&UDPWORD,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2],2);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = UDPWORD * signalItor->fGain;
						else
							fRes = UDPWORD;
						signalItor->valuesItor->second.push_back(fRes);
						log->Debug ()<<"正在解析一段完整报文: UDP_WORD  value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*2<<endl;
					}
	
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_DWORD:/*32bit*/;
			{
				do 
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						UDPDWORD = *(unsigned int*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4];
						//memcpy(&UDPDWORD,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4],4);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = UDPDWORD * signalItor->fGain;
						else
							fRes = UDPDWORD;
						signalItor->valuesItor->second.push_back(fRes);
						log->Debug ()<<"正在解析一段完整报文: UDP_DWORD value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*4<<endl;
					}
					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_INT:/*short int 16bit*/;
			{
				do 
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						UDPINT = *(short int*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset*2];
						//memcpy(&UDPINT,&_allTelegram[_index+ signalItor->fOffset_int/* + iOffset*2*/],2);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = UDPINT * signalItor->fGain;
						else
							fRes = UDPINT;
						signalItor->valuesItor->second.push_back(fRes);
						log->Debug ()<<"正在解析一段完整报文: UDP_INT value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*2<<endl;
					}

					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
			case DataType::UDP_DINT:/*int 32bit*/;
			{
				do 
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						UDPDINT = *(int*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4];
						//memcpy(&UDPDINT,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4],4);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = UDPDINT * signalItor->fGain;
						else
							fRes = UDPDINT;
						signalItor->valuesItor->second.push_back(fRes);
						log->Debug ()<<"正在解析一段完整报文: UDP_DINT value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*4<<endl;
					}

					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}

			case DataType::UDP_REAL:	/*32bit*/;
			{
				do 
				{
					for(iOffset = 0;iOffset < signalItor->iSamplePoints; ++ iOffset)
					{
						fRes = *(float*)&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4];
						//memcpy(&fRes,&_allTelegram[_index+ signalItor->fOffset_int + iOffset*4],4);
						if(signalItor->isAnalog&&!signalItor->fGain_is_1)
							fRes = fRes * signalItor->fGain;

						signalItor->valuesItor->second.push_back(fRes);
						log->Debug ()<<"正在解析一段完整报文: UDP_REAL value = "<<fRes<<"  fOffset = "<<signalItor->fOffset_int + iOffset*4<<endl;
					}

					_index += m_module->m_moduleConfig.iTelegramLength;
				} while( _index < toatlLen);
				_index = 0;
				break;
			}
		}
	}
	return true;
}