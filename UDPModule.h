/*********************************************************************
 *		
 *  文    件:    UDPModule.h          //TODO 存放module的配置信息,以及数据缓冲区,解析线程
 *
 *  版权所有:    Shanghai Baosight Software Co., Ltd.
 *
 *  概述
 *      : 存放module的配置信息,以及数据缓冲区,解析线程
 *   
 *
 *  版本历史		
 *      1.0    2010-06-08   Shen Huaibin    实现   
*********************************************************************/
#ifndef UDPMODULE_H
#define UDPMODULE_H
#include <global.h>
#include "MultiBuffer.h"
#include "NewMultiBuffer.h"
#include "BufferPharser.h"

#define CHECK_TIME_CYCLE 60000		//检测时间周期为十分钟

class CUDPConfigReader;
class CUDPModule : CSYSTimer
{
	friend CUDPConfigReader;
	friend CBufferPharser;
public:
	struct ModuleConfig
	{
		struct UDPSignal
		{
			// 是模拟量信号还是数字量
			bool        isAnalog;	
			// 放大系数
			float       fGain;  
			bool        fGain_is_1;
			// 信号No
			std::string strNo;			
			// 信号值起点
			float		fOffset;
			int         fOffset_int;
			int         fOffset_dec;
			// 信号值的数据类型
			DataType	eDataType;		
			// 信号值个数
			int         iSamplePoints;

			std::map<std::string,std::vector<double>>::iterator valuesItor;
		};
		enum nboType
		{
			AUTO_DETECTI = -1,
			LIT_ENDIAN,
			BIG_ENDIAN
		};
		// module info
		//模块No
		int  iModuleNo;	
		//端口号
		int  iPort;
		int  iModuleIndex;
		//报文长度
		int  iTelegramLength;			
		int  iTimebase;					//
		bool bIsNewUDPModule;
		DWORD dwMulticastGroup;//多播IP
		short sDestinationPort;//多播端口
		DWORD dwSourceAddress;//数据源IP地址，用以过滤和确定目标包的凭据
		ModuleType moduleType;
		//是否高低字节转换或者atuodetect
		nboType  eNBO;					
		// 所有的信号列表,按照偏移量从小到大排序
		std::vector<UDPSignal> vSignals;
		// 本次解析的数据值
		std::map<std::string,std::vector<double>> mSignalsValue;
	};

private:
	CSimpleMutex lock;


	//扫描次数
	unsigned int m_scanningTimes;
	bool		 m_bFirstArrived;
	//TODO: 当前是否是脏数据.
	bool         m_bPreStatus;
	int          m_iPacketStatus;
	//TODO: 接收数据的缓存区
	CMultiBuffer* m_pMultiBuffer;
	//TODO: 接收数据的新缓存区
	CNewMultiBuffer* m_pNewMultiBuffer;
	//TODO: 解析操作对象
	CBufferPharser m_bufferPharser;

	//TODO: 计算解析周期
	int     calcPharsePeriod();
	//TODO: 电文解析线程
	virtual void timerProc();
	//TODO: 将信号根据偏移量从小到大排序,以便按顺序解析电文
	static bool less_signal(CUDPModule::ModuleConfig::UDPSignal& a,CUDPModule::ModuleConfig::UDPSignal& b);
	//TODO: 解析完数据后,上抛数据到业务逻辑层
	typedef void (*fnOnAcquisition)(int _moduleNo,std::map<std::string,std::vector<double>>& _mValues,int _iPacketStatus);
	fnOnAcquisition m_fnOnAcquisition;
	bool m_bEnableTimer;

	//TODO: 丢包计数
	unsigned int    m_iLostPackageCount;
	unsigned int    m_iCurrentPackageCount;
	unsigned int    m_iBeginPackageCount;
	//TODO: 当前网络状态
	bool			m_bCurrentStatus;
	CSimpleMutex    m_lockCurrentStatus;
	//TODO: 获得网络状态
	bool getCurrentStatus();
	//TODO: 设置网络状态
	void setCurrentStatus(bool _currentStatus);
public:
	void NewTimerProc();
	//TODO: 统计接收的数据包
	void setTotalPackage(int totalPackage);
	//TODO: 设置是否高低字节转换
	void setNboType(CUDPModule::ModuleConfig::nboType type);
	//TODO: 是否高低字节转换或者atuodetect
	int  nboType(void);
	//TODO: 返回报文端口
	int  getPort(void);
	//TODO: 返回本module电文长度
	int  getTelegramLen(void);
	//TODO: 向缓冲区写入数据
	int  write(const void* _buffer);
	int  newWrite(const void* _buffer,int length = -1);
	//TODO: 停止解析
	bool stopPharse(void);
	//TODO: 开始解析
	bool startPharse(fnOnAcquisition _fnOnAcquisition,bool enableTimer=true);
	//TODO: 获取网络状态
	int	 getPacketStatus(void){ return m_iPacketStatus; }

	CUDPModule(void);
	virtual ~CUDPModule(void);

public:
	//TODO: 该模块电文的配置信息
	ModuleConfig m_moduleConfig;
	unsigned int	m_packageCount;
	long long		m_dataCounts;
	clock_t			m_startTime;
	clock_t			m_currentTime;
	// 检测时钟时间
	unsigned int	m_checkTime;
	// 
	bool			m_isPackageNormal;
	unsigned int	m_uiUDPCachPeriod;
	//时钟基准
	unsigned int m_uiSoftTimebase;
};
#endif//UDPMODULE_H

