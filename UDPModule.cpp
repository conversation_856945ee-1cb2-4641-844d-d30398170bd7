#include "UDPModule.h"
#include "UDPDriver.h"
extern CUDPDriver* thePlugin;

CUDPModule::CUDPModule(void)
: CSYSTimer(m_moduleConfig.iTimebase)
, m_bufferPharser(this)
, m_iCurrentPackageCount(0)
, m_iLostPackageCount(0)
, m_bCurrentStatus(true)
, m_bPreStatus(false)
, m_iPacketStatus(2)
, m_packageCount(0)
, m_checkTime(CHECK_TIME_CYCLE)
, m_bFirstArrived(true)
, m_scanningTimes(0)
{
	m_pMultiBuffer = nullptr;
	m_pNewMultiBuffer = nullptr;
	m_startTime = 0;
	m_currentTime = 0;
	m_dataCounts = 0;
	m_isPackageNormal = true;

}

CUDPModule::~CUDPModule(void)
{
	// 停止解析
	std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CUDPModule::~CUDPModule"));
	log->Info ("停止解析");
	stopTimer ();
	if (m_pMultiBuffer != nullptr)
	{
		delete m_pMultiBuffer;
		m_pMultiBuffer = nullptr;
	}
	if (m_pNewMultiBuffer != nullptr)
	{
		delete m_pNewMultiBuffer;
		m_pNewMultiBuffer = nullptr;
	}
}

/**********************************************************************
*  概述:  设置当前总共应该接收的数据包的个数
*  返回值:  无
*  参数列表:
*    totalPackage: [IN]   [描述] 目前为止应该接收到的数据包个数
*
*  版本历史		
*       1.0   2010:6:24 16:18   Shenhuaibin    实现
*  
**********************************************************************/	
void CUDPModule::setTotalPackage(int totalPackage)
{


	std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CUDPModule::setTotalPackage"));
	
	m_iCurrentPackageCount == 0 ? totalPackage : m_iCurrentPackageCount;
	int lostCount = m_iCurrentPackageCount - totalPackage;
	// 刚开始采集
	if (m_iCurrentPackageCount == 0)
	{
		m_iLostPackageCount = 0;
		m_iBeginPackageCount = totalPackage;
	}
	if (lostCount < -1 && m_iCurrentPackageCount != 0)
	{
		// 网络是否正常
		if(getCurrentStatus ())
		{
			m_iLostPackageCount -= lostCount +1;
			log->Fatal ()<<"模块 [:"<<m_moduleConfig.iModuleIndex<<"] 发现丢包,当前丢包总数 ["<<m_iLostPackageCount<<"] 当前包计数为 ["<<totalPackage<<"]"<<endl;
		}else
		{
			// 恢复网络正常
			setCurrentStatus(true);
			m_iBeginPackageCount = totalPackage;
			m_iLostPackageCount = 0;
		}
	}
	m_iCurrentPackageCount = totalPackage;
}
/**********************************************************************
*  概述:  设置是否需要高低字节转换
*  返回值:  无
*  参数列表:
*    type: [IN]   [描述] 报文类型
*
*  版本历史		
*       1.0   2010:6:24 17:22   Shenhuaibin    实现
*  
**********************************************************************/	
void CUDPModule::setNboType(CUDPModule::ModuleConfig::nboType type)
{
	m_moduleConfig.eNBO = type;
}
/**********************************************************************
*  概述:  向缓冲区写入数据
*  返回值: int [描述] 写入的字节数
*  参数列表:
*    _buffer : [IN]   [描述] 要写入的buffer
*
*  版本历史		
*       1.0   2010:6:9 10:32   Shenhuaibin    实现
*  
**********************************************************************/	
int CUDPModule::write(const void* _buffer)
{
	std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CUDPModule::write"));
	log->Debug ("向缓冲区写入数据");
	return m_pMultiBuffer->write (_buffer,m_moduleConfig.iTelegramLength);
}

int CUDPModule::newWrite(const void* _buffer,int _bufferLength)
{
	std::auto_ptr<ILog> log(thePlugin->getRootLog()->GetLogInstance("CUDPModule::write"));
	log->Debug("向缓冲区写入数据");
	int result = 0;
	if (_bufferLength > 0)
	{
		result = m_pNewMultiBuffer->Write(_buffer, _bufferLength);
	}
	else
	{
		result = m_pNewMultiBuffer->Write(_buffer, m_moduleConfig.iTelegramLength);
	}
	return result;
}
/**********************************************************************
*  概述:  返回本module电文长度
*  返回值: int [描述] 电文长度
*  参数列表:
*    无
*
*  版本历史		
*       1.0   2010:6:9 10:31   Shenhuaibin    实现
*  
**********************************************************************/	
int CUDPModule::getTelegramLen(void)
{
	return m_moduleConfig.iTelegramLength;
}

void CUDPModule::NewTimerProc()
{
	std::auto_ptr<ILog> log(thePlugin->getRootLog()->GetLogInstance("CUDPModule::NewTimerProc"));
	//log->Fatal("------------------");
	lock.lock();
	if (m_pNewMultiBuffer == nullptr)
	{
		lock.unlock();
		return;
	}
	m_scanningTimes++;
	if (m_scanningTimes*m_uiSoftTimebase >= m_moduleConfig.iTimebase)
	{
		//log->Fatal() << "----------次数:" << m_scanningTimes << "基准:" << m_uiSoftTimebase << "No:" << m_moduleConfig.iModuleNo << "timebase:" << m_moduleConfig.iTimebase << endl;;
		m_scanningTimes = 0;
		//log->Fatal() << "No:" << m_moduleConfig.iModuleNo << " 开始解析：" << m_pNewMultiBuffer << endl;
		std::vector<unsigned char>& _allTelegram = m_pNewMultiBuffer->Read();
		if (m_pNewMultiBuffer->GetReadCount()*m_moduleConfig.iTimebase > 5 * 1000)
		{
			m_iPacketStatus = 1;
		}
		else
		{
			m_iPacketStatus = 0;
		}
		// 解析电文
		bool isNormal = m_bufferPharser.pharser(_allTelegram,m_moduleConfig.moduleType == ModuleType::MULTICAST);
		//log->Fatal() << "No:" << m_moduleConfig.iModuleNo << " 解析完成" << endl;
		// 将数据上抛至逻辑层
		if (m_bufferPharser.isGetFirstPackage())
		{
			//原来上抛的逻辑里按照永远正常的逻辑上抛
			m_fnOnAcquisition(m_moduleConfig.iModuleNo, m_moduleConfig.mSignalsValue, 0);
			if (m_bFirstArrived)
			{
				//log->Debug() << "################################端口:" << getPort() << "的ModuleIndex:" << m_moduleConfig.iModuleIndex << "解析完毕 " << endl;
				m_bFirstArrived = false;
			}
		}

		// 添加完毕后,清空数据,继续解析数据.
		for (std::map<std::string, std::vector<double>>::iterator itor = m_moduleConfig.mSignalsValue.begin(); \
			itor != m_moduleConfig.mSignalsValue.end(); \
			++itor)
		{
			itor->second.clear();
		}
	}
	lock.unlock();
}

/**********************************************************************
*  概述:  数据解析线程
*  返回值: 无
*  参数列表:
*    无
*
*  版本历史		
*       1.0   2010:6:1 13:34   Shenhuaibin    实现
*  
**********************************************************************/	
void CUDPModule::timerProc()
{
	std::auto_ptr<ILog> log(thePlugin->getRootLog()->GetLogInstance("CUDPModule::timerProc"));
	//log->Fatal() << "--------- 多缓冲区模式";
	//log->Fatal("------------------");
	DWORD dwS = ::GetTickCount();
	//std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CUDPModule::timerProc"));
	//log->Info()<<"等待待解析的数据 端口:"<<getPort ()<<"  ModuleIndex:"<<m_moduleConfig.iModuleIndex<<endl;
	std::vector<unsigned char>& _allTelegram = m_pMultiBuffer->read ();
	// 解析电文
	bool isNormal = m_bufferPharser.pharser(_allTelegram);
	
	//log->Info ()<<"ModuleIndex:"<<m_moduleConfig.iModuleIndex<<"解析完毕"<<endl;
	// 将数据上抛至逻辑层
	if(m_bufferPharser.isGetFirstPackage())
	{
		if (m_bPreStatus != isNormal)
		{//改变状态,奇数为状态发生改变.偶数为维持前一状态
			//正常包		m_iPacketStatus = 0
			//true->false   m_iPacketStatus = 1,发生异常
			//脏数据	    m_iPacketStatus = 2
			//false->true   m_iPacketStatus = 3,恢复正常
			++m_iPacketStatus;
			m_bPreStatus = isNormal;
		}
		//log->Fatal() << "ModuleIndex:" << m_moduleConfig.iModuleIndex << "状态值:" << m_iPacketStatus << endl;

		m_fnOnAcquisition(m_moduleConfig.iModuleNo,m_moduleConfig.mSignalsValue,m_iPacketStatus);
		if (m_bFirstArrived)
		{
			log->Debug()<<"################################端口:"<<getPort ()<<"的ModuleIndex:"<<m_moduleConfig.iModuleIndex<<"解析完毕 "<<endl;
			m_bFirstArrived = false;
		}
		// 如果是状态发生改变则进入维持状态标识
		if (m_iPacketStatus%2==1)
		{
			if(++m_iPacketStatus==4)
			{
				m_iPacketStatus = 0;
			}
		}
	}

	// 添加完毕后,清空数据,继续解析数据.
	for(std::map<std::string,std::vector<double>>::iterator itor = m_moduleConfig.mSignalsValue.begin();\
		itor != m_moduleConfig.mSignalsValue.end();\
		++ itor)
	{
		itor->second.clear();
	}
	DWORD dwE=::GetTickCount();
	//log->Info()<<"################################端口:"<<getPort ()<<"的ModuleIndex:"<<m_moduleConfig.iModuleIndex<<"解析完毕 "<<dwE-dwS<<endl;
	Sleep(0);
}

/**********************************************************************
*  概述:  开始解析
*  返回值: bool [描述] 操作结果
*  参数列表:
*     _fnOnAcquisition : [IN]   [描述] 数据上抛回调函数
*
*  版本历史		
*       1.0   2010:6:9 9:47   Shenhuaibin    实现
*  
**********************************************************************/	
bool CUDPModule::startPharse(fnOnAcquisition _fnOnAcquisition, bool _enableTimer)
{
	std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CUDPModule::startPharse"));
	//log->Fatal() << "startPharse111" << endl;

	if (m_moduleConfig.bIsNewUDPModule)
		m_iPacketStatus = 0;
	m_fnOnAcquisition = _fnOnAcquisition;
	m_bEnableTimer = _enableTimer;
	// 创建数据缓存区:
	// 计算buffer长度
	//log->Info ()<<"创建数据缓存区于端口:"<<getPort ()<<"的ModuleIndex:"<<m_moduleConfig.iModuleIndex<<endl;
	if (m_moduleConfig.iTimebase <= 0.01){   return false;	}
	//log->Fatal() << "startPharse222" << endl;

	// 计算解析周期:该时间是精确时间.
	//int time = calcPharsePeriod();
	/*if (m_uiUDPCachPeriod != 100)
	{
		m_uiUDPCachPeriod = calcPharsePeriod();
	}*/
	int time = calcPharsePeriod();
	//log->Info ()<<"ModuleIndex: "<<m_moduleConfig.iModuleIndex<<" 的解析周期为: "<<time<<" 端口号为: "<<m_moduleConfig.iPort<<endl;
	// 网络异常时,补充的数据包的个数
	int packageNumber = time / m_moduleConfig.iTimebase;
	if (_enableTimer)
	{
		m_bufferPharser.setPackageNumber(packageNumber);
	}
	else
	{
		m_bufferPharser.setPackageNumber(0);
	}
	//log->Fatal() << "startPharse333" << endl;

	// 计算预分配内存空间尺寸:由于定时器有误差,所以多分配3个包的长度
	size_t size = (packageNumber+3) * m_moduleConfig.iTelegramLength;
	if (m_moduleConfig.bIsNewUDPModule || m_moduleConfig.moduleType == ModuleType::MULTICAST)
	{
		//log->Fatal() << "创建 m_pNewMultiBuffer" << endl;
		m_pNewMultiBuffer = new CNewMultiBuffer(size);
		//log->Fatal() << "m_pNewMultiBuffer创建成功:" << &m_pNewMultiBuffer<< endl;
	}
	else
	{
		m_pMultiBuffer = new CMultiBuffer(size);
	}

	// 设置每个信号的整数和小数部分.并且设置数据数组大小
	for (std::vector<CUDPModule::ModuleConfig::UDPSignal>::iterator signalItor = m_moduleConfig.vSignals.begin ();\
		signalItor != m_moduleConfig.vSignals.end ();\
		++ signalItor)
	{
		//bool量且是多播时，靠读取配置文件获取整数和小数部分。
		if (!signalItor->isAnalog && m_moduleConfig.moduleType == ModuleType::MULTICAST)
		{
			//相反的条件比较好写，do nothing
		}
		else
		{
			// 整数位
			signalItor->fOffset_int = (((int)(signalItor->fOffset * 10)) + 1) / 10;
			// 小数位
			signalItor->fOffset_dec = ((((int)(signalItor->fOffset * 100)) % 100) + 1) / 10;
		}
		// 放大倍数是否为1
		if(abs(signalItor->fGain - 1.0)<=0.001) 
			signalItor->fGain_is_1 = true;
		else
			signalItor->fGain_is_1 = false;
		// 获得到存储该信号的数组
		signalItor->valuesItor = m_moduleConfig.mSignalsValue.find (std::map<std::string,std::vector<float>>::key_type(signalItor->strNo));
		
		signalItor->valuesItor->second.reserve((packageNumber*signalItor->iSamplePoints+8)*sizeof(float));
	}

	// 初始化状态变量
	m_iCurrentPackageCount = 0;
	m_iLostPackageCount = 0;
	m_iBeginPackageCount = 0;
	m_bCurrentStatus = true;
	//log->Fatal() << "startPharse444" << endl;

	if (_enableTimer)
		return this->startTimer(time);
	else
		return true;
}
/**********************************************************************
*  概述:  停止解析
*  返回值: bool [描述] 操作结果
*  参数列表:
*    无
*
*  版本历史		
*       1.0   2010:6:9 9:47   Shenhuaibin    实现
*  
**********************************************************************/	
bool CUDPModule::stopPharse(void)
{
	std::auto_ptr<ILog> log(thePlugin->getRootLog ()->GetLogInstance ("CUDPModule::stopPharse"));

	log->Info ("停止解析数据,清除数据缓存区");
	// 停止时钟周期
	
	// 清除缓冲区
	if (m_pMultiBuffer != nullptr)
	{
		delete m_pMultiBuffer;
		m_pMultiBuffer = nullptr;
	}
	if (m_pNewMultiBuffer != nullptr)
	{
		delete m_pNewMultiBuffer;
		m_pNewMultiBuffer = nullptr;
	}
	if (m_bEnableTimer)
	{
		return stopTimer();
	}
	else
	{
		return true;
	}
}

/**********************************************************************
*  概述:  返回报文端口
*  返回值: int [描述] 报文端口
*  参数列表:
*    无
*
*  版本历史		
*       1.0   2010:6:9 10:51   Shenhuaibin    实现
*  
**********************************************************************/	
int  CUDPModule::getPort(void)
{
	return m_moduleConfig.iPort;
}
/**********************************************************************
*  概述:  将信号根据偏移量从小到大排序:stl中sort函数
*  返回值: bool [描述] 
*  参数列表:
*    a: [IN]   [描述] 一个信号
*    b: [IN]   [描述] 另外一个信号
*
*  版本历史		
*       1.0   2010:6:4 16:51   Shenhuaibin    实现
*  
**********************************************************************/	
bool CUDPModule::less_signal(CUDPModule::ModuleConfig::UDPSignal& a,CUDPModule::ModuleConfig::UDPSignal& b)
{
	return a.fOffset<b.fOffset;
}
/**********************************************************************
*  概述:  该module电文是否高低字节转换
*  返回值: bool [描述]	true:需要转换 flase:不需要转换
*  参数列表:
*    无
*
*  版本历史		
*       1.0   2010:6:10 12:29   Shenhuaibin    实现
*  
**********************************************************************/	
int CUDPModule::nboType(void)
{
	return m_moduleConfig.eNBO;
}


/**********************************************************************
*  概述:  计算解析周期
*  返回值: int [描述]   精确的解析周期
*  参数列表:
*    无
*
*  版本历史		
*       1.0   2010:6:24 16:17   Shenhuaibin    实现
*  
**********************************************************************/	
int CUDPModule::calcPharsePeriod()
{
	// 设置解析定时器的基准频率
	this->setRes (m_moduleConfig.iTimebase);
	// 进行一次数据解析的准确时间
	srand(time(0));
	if (m_moduleConfig.iTimebase<=30)
	{
		//400/(8 ~ 30) = 13 ~ 50 个包
		return (400/m_moduleConfig.iTimebase)*m_moduleConfig.iTimebase;
	}else if (m_moduleConfig.iTimebase <= 100)
	{
		//(400 ~ 500)/(30 ~ 100) = 4 ~ 16 个包
		return ((400 + rand ()%100)/m_moduleConfig.iTimebase)*m_moduleConfig.iTimebase;
	}else if (m_moduleConfig.iTimebase <= 200)
	{
		//(500 ~ 600)/(100 ~ 200) = 2 ~ 6 个包
		return ((500 + rand ()%100)/m_moduleConfig.iTimebase)*m_moduleConfig.iTimebase;
	}else
	{
		//(650 ~ oo)/(200 ~ oo) = 3
		return 3*m_moduleConfig.iTimebase;
	}

	return 3*m_moduleConfig.iTimebase;;
}
/**********************************************************************
*  概述:  设置当前网络状态
*  返回值:  [描述]	无
*  参数列表:
*    _currentStatus: [IN]   [描述] 当前网络状态
*
*  版本历史		
*       1.0   2010:6:25 15:40   Shenhuaibin    实现
*  
**********************************************************************/	
void CUDPModule::setCurrentStatus(bool _currentStatus)
{
	m_lockCurrentStatus.lock ();
		m_bCurrentStatus = _currentStatus;
	m_lockCurrentStatus.unlock ();
}
/**********************************************************************
*  概述:  获取当前网络状态
*  返回值: bool [描述]	网络当前状态
*  参数列表:
*    无
*
*  版本历史		
*       1.0   2010:6:28 10:50   Shenhuaibin    实现
*  
**********************************************************************/	
bool CUDPModule::getCurrentStatus()
{
	m_lockCurrentStatus.lock ();
	bool _currentStatus	= m_bCurrentStatus;
	m_lockCurrentStatus.unlock ();

	return _currentStatus;
}