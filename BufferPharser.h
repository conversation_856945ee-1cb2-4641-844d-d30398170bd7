/*********************************************************************
 *		
 *  文    件:    BufferPharser.h          //TODO 针对MultiBuffer的数据解析
 *
 *  版权所有:    Shanghai Baosight Software Co., Ltd.
 *
 *  概述
 *      : 针对MultiBuffer的数据解析
 *      ://TODO
 *
 *  版本历史		
 *      1.0    2010-06-01   Shen Huaibin    //TODO请添加本次主要修改内容   
*********************************************************************/
#ifndef BUFFERPHARSER_H
#define BUFFERPHARSER_H
#include "UDPConfigReader.h"

class CBufferPharser
{
private:
	//TODO: 解析报文
	//Big-Endian: 低地址存放高位
	class CBigPharser
	{
		// 电文配置信息
		CUDPModule* m_module;
	public:
		bool pharserOneTelegram(vector<unsigned char>& _allTelegram,int _index);
		bool pharser(IN std::vector<unsigned char>& _allTelegram);
		bool pharserMulticast(IN std::vector<unsigned char>& _allTelegram);

		CBigPharser(CUDPModule* _aModule):m_module(_aModule){};
	};
	//Little-Endian: 低地址存放低位
	class CLitPharser
	{
		// 电文配置信息
		CUDPModule* m_module;

	public:
		bool pharserOneTelegram(vector<unsigned char>& _allTelegram,int _index);
		bool pharser(IN std::vector<unsigned char>& _allTelegram);
		bool pharserMulticast(IN std::vector<unsigned char>& _allTelegram);

		CLitPharser(CUDPModule* _aModule):m_module(_aModule){};
	};
private:
	CBigPharser m_bigPharser;
	CLitPharser m_litPharser;
	CUDPModule* m_module;
	//TODO: 标记是否已经收到第一个包
	bool        m_bGetFirstPackage;
	//TODO: 需要补充的包的数量
	int			m_iPackageNumber;
	//TODO: 补充丢失的包
	void		completePackage(int _number);
	//TODO: 记录丢失包的次数
	int         m_iMissPackageCount;
public:
	//TODO: 解析电文内容
	bool pharser(IN std::vector<unsigned char>& _allTelegram,bool isMulticast = false);
	//TODO: 本次解析的内存中包含的数据包数量
	void setPackageNumber(int _packageNumber);
	//TODO: 是否已经收到第一个包
	inline bool isGetFirstPackage(){return m_bGetFirstPackage;};
	CBufferPharser(CUDPModule* _aModule);
	virtual ~CBufferPharser(void);
};

#endif//BUFFERPHARSER_H
